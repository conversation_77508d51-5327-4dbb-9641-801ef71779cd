package com.base.study.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.study.domain.StudyAttendance;
import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.domain.StudyClass;
import com.base.study.domain.StudyCourse;
import com.base.study.domain.StudyRegistration;
import com.base.study.mapper.*;
import com.base.study.mapper.StudyCourseMapper;
import com.base.study.utils.DeviceApiUtil;
import com.base.system.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤设备服务类
 * 基于现有系统设计，利用班级表的device_ids字段关联设备
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Service
public class DeviceService {
    
    private static final Logger log = LoggerFactory.getLogger(DeviceService.class);
    
    @Autowired
    private StudyAttendanceDeviceMapper deviceMapper;
    
    @Autowired
    private StudyClassMapper classMapper;
    
    @Autowired
    private StudyRegistrationMapper registrationMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private StudyAttendanceMapper attendanceMapper;

    @Autowired
    private StudyCourseMapper courseMapper;
    
    /**
     * 测试设备连接
     */
    public boolean testConnection(StudyAttendanceDevice device) {
        try {
            String url = buildDeviceUrl(device) + "/ISAPI/System/deviceInfo";
            
            String response = DeviceApiUtil.get(url, device.getUsername(), device.getPassword());
            
            if (StringUtils.isNotEmpty(response)) {
                log.info("设备连接测试成功: {}", device.getDeviceName());
                return true;
            }
        } catch (Exception e) {
            log.error("设备连接测试失败: {}, 错误: {}", device.getDeviceName(), e.getMessage());
        }
        return false;
    }
    
    /**
     * 启用设备布防 (f0001功能)
     * 基于海康威视设备的布防方式，通过长连接实现事件监听
     */
    public boolean startGuard(StudyAttendanceDevice device) {
        try {
            // 海康威视设备的布防实际上是启动长连接事件监听
            // 这里我们先测试设备是否支持事件通知接口
            String testUrl = buildDeviceUrl(device) + "/ISAPI/Event/notification/alertStream";
            
            // 使用GET请求测试接口是否可用
            String response = DeviceApiUtil.get(testUrl, device.getUsername(), device.getPassword());
            
            // 如果接口可用，说明布防启动成功
            log.info("设备布防启用成功: {} - 事件监听接口已激活", device.getDeviceName());
            return true;
            
        } catch (Exception e) {
            log.error("设备布防启用失败: {}, 错误: {}", device.getDeviceName(), e.getMessage());
            
            // 如果事件监听接口不支持，尝试使用传统的VMD配置方式
            try {
                String vmdUrl = buildDeviceUrl(device) + "/ISAPI/System/Video/inputs/channels/1/motionDetection";
                
                // 构建移动侦测配置来实现类似布防的功能
                String motionConfig = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                    "<MotionDetection version=\"2.0\" xmlns=\"http://www.hikvision.com/ver20/XMLSchema\">" +
                    "<enabled>true</enabled>" +
                    "<enableHighlight>false</enableHighlight>" +
                    "<samplingInterval>2</samplingInterval>" +
                    "<startTriggerTime>500</startTriggerTime>" +
                    "<endTriggerTime>500</endTriggerTime>" +
                    "</MotionDetection>";
                
                String vmdResponse = DeviceApiUtil.put(vmdUrl, motionConfig, 
                    device.getUsername(), device.getPassword());
                
                if (StringUtils.isNotEmpty(vmdResponse)) {
                    log.info("设备布防启用成功（通过移动侦测）: {}", device.getDeviceName());
                    return true;
                }
            } catch (Exception vmdException) {
                log.error("移动侦测配置也失败: {}", vmdException.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 关闭设备布防 (f0002功能)
     */
    public boolean stopGuard(StudyAttendanceDevice device) {
        try {
            // 对于海康威视设备，关闭布防主要是停止事件监听
            // 这里我们通过关闭移动侦测来实现
            String vmdUrl = buildDeviceUrl(device) + "/ISAPI/System/Video/inputs/channels/1/motionDetection";
            
            // 构建关闭移动侦测的配置
            String motionConfig = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<MotionDetection version=\"2.0\" xmlns=\"http://www.hikvision.com/ver20/XMLSchema\">" +
                "<enabled>false</enabled>" +
                "<enableHighlight>false</enableHighlight>" +
                "<samplingInterval>2</samplingInterval>" +
                "<startTriggerTime>500</startTriggerTime>" +
                "<endTriggerTime>500</endTriggerTime>" +
                "</MotionDetection>";
            
            String response = DeviceApiUtil.put(vmdUrl, motionConfig, 
                device.getUsername(), device.getPassword());
            
            if (StringUtils.isNotEmpty(response)) {
                log.info("设备布防关闭成功: {}", device.getDeviceName());
                return true;
            }
        } catch (Exception e) {
            log.error("设备布防关闭失败: {}, 错误: {}", device.getDeviceName(), e.getMessage());
            
            // 如果移动侦测接口不支持，尝试其他方式
            try {
                // 简单地返回成功，因为有些设备可能不支持动态配置
                log.info("设备布防关闭完成（设备可能不支持动态配置）: {}", device.getDeviceName());
                return true;
            } catch (Exception fallbackException) {
                log.error("布防关闭备用方案也失败: {}", fallbackException.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 同步班级学员到设备
     * 在课程开始前2小时调用
     */
    public int syncClassStudentsToDevice(Long classId) {
        try {
            // 获取班级信息
            StudyClass studyClass = classMapper.selectStudyClassByClassId(classId);
            if (studyClass == null || StringUtils.isEmpty(studyClass.getDeviceIds())) {
                log.warn("班级不存在或未配置考勤设备: classId={}", classId);
                return 0;
            }
            
            // 获取班级关联的设备列表
            List<StudyAttendanceDevice> devices = getDevicesByIds(studyClass.getDeviceIds());
            if (devices.isEmpty()) {
                log.warn("班级关联的考勤设备不存在: classId={}, deviceIds={}", classId, studyClass.getDeviceIds());
                return 0;
            }
            
            // 获取班级学员列表
            List<SysUser> students = getClassStudents(classId);
            if (students.isEmpty()) {
                log.warn("班级无学员: classId={}", classId);
                return 0;
            }
            
            int totalSyncCount = 0;
            
            // 对每个设备同步学员信息
            for (StudyAttendanceDevice device : devices) {
                int deviceSyncCount = syncStudentsToDevice(device, students);
                totalSyncCount += deviceSyncCount;
                log.info("班级[{}]学员同步到设备[{}]完成: 成功{}人", classId, device.getDeviceName(), deviceSyncCount);
            }
            
            log.info("班级[{}]学员同步完成: 总计成功{}人", classId, totalSyncCount);
            return totalSyncCount;
            
        } catch (Exception e) {
            log.error("同步班级学员到设备失败: classId={}, 错误: {}", classId, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 从设备删除班级学员
     * 在课程结束后2小时调用
     */
    public int removeClassStudentsFromDevice(Long classId) {
        try {
            // 获取班级信息
            StudyClass studyClass = classMapper.selectStudyClassByClassId(classId);
            if (studyClass == null || StringUtils.isEmpty(studyClass.getDeviceIds())) {
                log.warn("班级不存在或未配置考勤设备: classId={}", classId);
                return 0;
            }
            
            // 获取班级关联的设备列表
            List<StudyAttendanceDevice> devices = getDevicesByIds(studyClass.getDeviceIds());
            if (devices.isEmpty()) {
                log.warn("班级关联的考勤设备不存在: classId={}, deviceIds={}", classId, studyClass.getDeviceIds());
                return 0;
            }
            
            // 获取班级学员列表
            List<SysUser> students = getClassStudents(classId);
            if (students.isEmpty()) {
                log.warn("班级无学员: classId={}", classId);
                return 0;
            }
            
            int totalRemoveCount = 0;
            
            // 从每个设备删除学员信息
            for (StudyAttendanceDevice device : devices) {
                int deviceRemoveCount = removeStudentsFromDevice(device, students);
                totalRemoveCount += deviceRemoveCount;
                log.info("从设备[{}]删除班级[{}]学员完成: 成功{}人", device.getDeviceName(), classId, deviceRemoveCount);
            }
            
            log.info("从设备删除班级[{}]学员完成: 总计成功{}人", classId, totalRemoveCount);
            return totalRemoveCount;
            
        } catch (Exception e) {
            log.error("从设备删除班级学员失败: classId={}, 错误: {}", classId, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 同步设备考勤记录到考勤表
     * f0001布防开启后自动调用
     */
    public int syncDeviceAttendanceRecords(StudyAttendanceDevice device, Date startTime, Date endTime) {
        try {
            String url = buildDeviceUrl(device) + "/ISAPI/AccessControl/AcsEvent?format=json";
            // 构建查询条件，补全海康ISAPI必需字段
            JSONObject searchCond = new JSONObject();
            JSONObject acsEventCond = new JSONObject();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'+08:00'");
            acsEventCond.put("searchID", "1");
            acsEventCond.put("maxResults", 5000);
            acsEventCond.put("searchResultPosition", 0);
            // 时间范围
            JSONObject timeRange = new JSONObject();
            timeRange.put("beginTime", dateFormat.format(startTime));
            timeRange.put("endTime", dateFormat.format(endTime));
            acsEventCond.put("TimeSpanList", new JSONArray().fluentAdd(timeRange));
            // 海康部分设备需要major/minor参数，按demo补全
            acsEventCond.put("major", 5); // 5=门禁事件
            acsEventCond.put("minor", 75); // 75=合法刷卡
            searchCond.put("AcsEventCond", acsEventCond);
            String response = DeviceApiUtil.post(url, searchCond.toJSONString(), device.getUsername(), device.getPassword());
            if (StringUtils.isEmpty(response)) {
                return 0;
            }
            JSONObject responseJson = JSON.parseObject(response);
            JSONObject acsEvent = responseJson.getJSONObject("AcsEvent");
            if (acsEvent == null) {
                return 0;
            }
            JSONArray infoList = acsEvent.getJSONArray("InfoList");
            if (infoList == null || infoList.isEmpty()) {
                return 0;
            }
            int syncCount = 0;
            for (int i = 0; i < infoList.size(); i++) {
                JSONObject eventInfo = infoList.getJSONObject(i);
                if (processAttendanceEvent(device, eventInfo)) {
                    syncCount++;
                }
            }
            log.info("设备[{}]考勤记录同步完成: 同步数量[{}]", device.getDeviceName(), syncCount);
            return syncCount;
        } catch (Exception e) {
            log.error("同步设备考勤记录失败: 设备[{}], 错误: {}", device.getDeviceName(), e.getMessage());
            return 0;
        }
    }
    
    /**
     * 根据设备ID列表获取设备信息
     */
    private List<StudyAttendanceDevice> getDevicesByIds(String deviceIds) {
        if (StringUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        
        String[] ids = deviceIds.split(",");
        List<StudyAttendanceDevice> devices = new ArrayList<>();
        
        for (String id : ids) {
            if (StringUtils.isNotEmpty(id.trim())) {
                try {
                    Long deviceId = Long.parseLong(id.trim());
                    StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
                    if (device != null) {
                        devices.add(device);
                    }
                } catch (NumberFormatException e) {
                    log.warn("设备ID格式错误: {}", id);
                }
            }
        }
        
        return devices;
    }
    
    /**
     * 获取班级学员列表
     */
    private List<SysUser> getClassStudents(Long classId) {
        // 通过报名表获取班级学员
        StudyRegistration queryParam = new StudyRegistration();
        queryParam.setClassId(classId);
        
        List<StudyRegistration> registrations = registrationMapper.selectStudyRegistrationList(queryParam);
        
        return registrations.stream()
            .map(registration -> sysUserMapper.selectUserById(registration.getUserId()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 同步学员到设备
     */
    private int syncStudentsToDevice(StudyAttendanceDevice device, List<SysUser> students) {
        int successCount = 0;
        
        for (SysUser student : students) {
            try {
                // 使用用户ID作为设备用户编号
                String deviceUserNo = String.valueOf(student.getUserId());
                
                if (addUserToDevice(device, student, deviceUserNo)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("同步学员到设备失败: 设备[{}], 用户[{}], 错误: {}", 
                    device.getDeviceName(), student.getNickName(), e.getMessage());
            }
        }
        
        return successCount;
    }
    
    /**
     * 从设备删除学员
     */
    private int removeStudentsFromDevice(StudyAttendanceDevice device, List<SysUser> students) {
        int successCount = 0;
        
        for (SysUser student : students) {
            try {
                // 使用用户ID作为设备用户编号
                String deviceUserNo = String.valueOf(student.getUserId());
                
                if (deleteUserFromDevice(device, deviceUserNo)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("从设备删除学员失败: 设备[{}], 用户[{}], 错误: {}", 
                    device.getDeviceName(), student.getNickName(), e.getMessage());
            }
        }
        
        return successCount;
    }
    
    /**
     * 添加用户到设备（如果用户已存在则先删除再添加）
     */
    private boolean addUserToDevice(StudyAttendanceDevice device, SysUser user, String deviceUserNo) {
        try {
            // 先尝试删除已存在的用户（忽略删除失败的情况）
            try {
                deleteUserFromDevice(device, deviceUserNo);
                log.debug("预删除用户完成: 设备[{}], 设备编号[{}]", device.getDeviceName(), deviceUserNo);
            } catch (Exception e) {
                log.debug("预删除用户失败（用户可能不存在）: 设备[{}], 设备编号[{}], 错误: {}",
                    device.getDeviceName(), deviceUserNo, e.getMessage());
            }

            String url = buildDeviceUrl(device) + "/ISAPI/AccessControl/UserInfo/Record?format=json";

            // 构建用户信息
            JSONObject userInfo = new JSONObject();
            JSONObject userInfoDetail = new JSONObject();

            userInfoDetail.put("employeeNo", deviceUserNo);
            userInfoDetail.put("name", user.getNickName());
            userInfoDetail.put("userType", "normal");

            // 有效期设置（一年）
            JSONObject valid = new JSONObject();
            valid.put("enable", true);
            valid.put("beginTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss", new Date()));

            Date endDate = DateUtils.addYears(new Date(), 1);
            valid.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss", endDate));
            userInfoDetail.put("Valid", valid);

            // 配置门禁权限 - 这是关键！
            userInfoDetail.put("doorRight", "1"); // 门权限字符串，"1"表示有权限访问门1

            // 配置权限计划
            JSONArray rightPlan = new JSONArray();
            JSONObject doorPlan = new JSONObject();
            doorPlan.put("doorNo", 1); // 门编号，通常为1
            doorPlan.put("planTemplateNo", "1"); // 计划模板编号，使用默认模板1
            rightPlan.add(doorPlan);
            userInfoDetail.put("RightPlan", rightPlan);

            userInfo.put("UserInfo", userInfoDetail);

            String response = DeviceApiUtil.post(url, userInfo.toJSONString(),
                device.getUsername(), device.getPassword());

            if (StringUtils.isNotEmpty(response)) {
                log.debug("用户添加到设备成功: 设备[{}], 用户[{}], 设备编号[{}]",
                    device.getDeviceName(), user.getNickName(), deviceUserNo);
                return true;
            }
        } catch (Exception e) {
            // 如果仍然报用户已存在的错误，尝试使用PUT方法更新
            if (e.getMessage() != null && e.getMessage().contains("employeeNoAlreadyExist")) {
                log.info("用户已存在，尝试使用PUT方法更新: 设备[{}], 用户[{}], 设备编号[{}]",
                    device.getDeviceName(), user.getNickName(), deviceUserNo);
                return updateUserToDevice(device, user, deviceUserNo);
            }
            log.error("用户添加到设备失败: 设备[{}], 用户[{}], 错误: {}",
                device.getDeviceName(), user.getNickName(), e.getMessage());
        }
        return false;
    }

    /**
     * 更新设备中的用户信息（使用PUT方法）
     */
    private boolean updateUserToDevice(StudyAttendanceDevice device, SysUser user, String deviceUserNo) {
        try {
            String url = buildDeviceUrl(device) + "/ISAPI/AccessControl/UserInfo/Modify?format=json";

            // 构建用户信息
            JSONObject userInfo = new JSONObject();
            JSONObject userInfoDetail = new JSONObject();

            userInfoDetail.put("employeeNo", deviceUserNo);
            userInfoDetail.put("name", user.getNickName());
            userInfoDetail.put("userType", "normal");

            // 有效期设置（一年）
            JSONObject valid = new JSONObject();
            valid.put("enable", true);
            valid.put("beginTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss", new Date()));

            Date endDate = DateUtils.addYears(new Date(), 1);
            valid.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss", endDate));
            userInfoDetail.put("Valid", valid);

            // 配置门禁权限 - 更新时也需要包含权限信息
            userInfoDetail.put("doorRight", "1"); // 门权限字符串

            // 配置权限计划
            JSONArray rightPlan = new JSONArray();
            JSONObject doorPlan = new JSONObject();
            doorPlan.put("doorNo", 1); // 门编号
            doorPlan.put("planTemplateNo", "1"); // 计划模板编号
            rightPlan.add(doorPlan);
            userInfoDetail.put("RightPlan", rightPlan);

            userInfo.put("UserInfo", userInfoDetail);

            String response = DeviceApiUtil.put(url, userInfo.toJSONString(),
                device.getUsername(), device.getPassword());

            if (StringUtils.isNotEmpty(response)) {
                log.debug("用户更新到设备成功: 设备[{}], 用户[{}], 设备编号[{}]",
                    device.getDeviceName(), user.getNickName(), deviceUserNo);
                return true;
            }
        } catch (Exception e) {
            log.error("用户更新到设备失败: 设备[{}], 用户[{}], 错误: {}",
                device.getDeviceName(), user.getNickName(), e.getMessage());
        }
        return false;
    }

    /**
     * 上传用户人脸图片到设备
     * 使用multipart/form-data格式上传文件
     */
    public boolean uploadUserFaceImage(StudyAttendanceDevice device, String deviceUserNo, String faceImageBase64) {
        try {
            if (faceImageBase64 == null || faceImageBase64.isEmpty()) {
                log.warn("用户[{}]未提供人脸base64，跳过上传", deviceUserNo);
                return false;
            }

            // 先删除已存在的人脸数据
            try {
                deleteFaceFromDevice(device, deviceUserNo);
                log.debug("预删除人脸数据完成: 设备[{}], 用户编号[{}]", device.getDeviceName(), deviceUserNo);
            } catch (Exception e) {
                log.debug("预删除人脸数据失败（数据可能不存在）: 设备[{}], 用户编号[{}], 错误: {}",
                    device.getDeviceName(), deviceUserNo, e.getMessage());
            }

            // 将base64数据转换为字节数组
            byte[] imageBytes = convertBase64ToBytes(faceImageBase64);
            if (imageBytes == null) {
                log.error("base64数据转换失败: 设备[{}], 用户编号[{}]", device.getDeviceName(), deviceUserNo);
                return false;
            }

            String apiUrl = buildDeviceUrl(device) + "/ISAPI/Intelligent/FDLib/FaceDataRecord?format=json";

            // 构建FaceDataRecord JSON字符串
            JSONObject faceDataRecord = new JSONObject();
            faceDataRecord.put("faceLibType", "blackFD");
            faceDataRecord.put("FDID", "1");
            faceDataRecord.put("FPID", deviceUserNo);

            log.info("人脸数据上传请求: 设备[{}], 用户编号[{}], 图片大小={}字节",
                device.getDeviceName(), deviceUserNo, imageBytes.length);

            // 使用multipart/form-data格式上传
            String response = DeviceApiUtil.postMultipart(apiUrl,
                faceDataRecord.toJSONString(), imageBytes,
                device.getUsername(), device.getPassword());

            if (StringUtils.isNotEmpty(response)) {
                log.info("用户人脸图片上传成功: 设备[{}], 用户编号[{}], 响应: {}",
                    device.getDeviceName(), deviceUserNo, response);
                return true;
            } else {
                log.warn("用户人脸图片上传失败: 设备[{}], 用户编号[{}]，无响应", device.getDeviceName(), deviceUserNo);
            }

        } catch (Exception e) {
            log.error("用户人脸图片上传失败: 设备[{}], 用户编号[{}], 错误: {}",
                device.getDeviceName(), deviceUserNo, e.getMessage());
        }
        return false;
    }

    /**
     * 将base64数据转换为字节数组
     */
    private byte[] convertBase64ToBytes(String faceImageBase64) {
        try {
            // 清理base64数据
            String cleanBase64 = faceImageBase64;
            if (cleanBase64.contains(",")) {
                cleanBase64 = cleanBase64.substring(cleanBase64.indexOf(",") + 1);
            }

            // 解码base64数据
            byte[] imageBytes = java.util.Base64.getDecoder().decode(cleanBase64);

            log.debug("base64数据转换成功: 原始长度={}, 字节数组长度={}",
                cleanBase64.length(), imageBytes.length);

            return imageBytes;

        } catch (Exception e) {
            log.error("base64数据转换失败: {}", e.getMessage());
            return null;
        }
    }





    /**
     * 从设备删除人脸数据
     */
    /**
     * 从设备删除人脸数据
     */
    private boolean deleteFaceFromDevice(StudyAttendanceDevice device, String deviceUserNo) {
        try {
            String url = buildDeviceUrl(device) + "/ISAPI/Intelligent/FDLib/FDSearch/Delete?format=json&FDID=1&faceLibType=blackFD";

            // 构建删除请求
            JSONObject deleteRequest = new JSONObject();
            deleteRequest.put("searchResultPosition", 0);
            deleteRequest.put("maxResults", 20);
            deleteRequest.put("faceLibType", "blackFD");
            deleteRequest.put("FDID", "1");
            deleteRequest.put("FPID", deviceUserNo);

            String response = DeviceApiUtil.put(url, deleteRequest.toJSONString(),
                device.getUsername(), device.getPassword());

            if (StringUtils.isNotEmpty(response)) {
                log.debug("人脸数据从设备删除成功: 设备[{}], 用户编号[{}]",
                    device.getDeviceName(), deviceUserNo);
                return true;
            }
        } catch (Exception e) {
            log.debug("人脸数据从设备删除失败: 设备[{}], 用户编号[{}], 错误: {}",
                device.getDeviceName(), deviceUserNo, e.getMessage());
        }
        return false;
    }

    /**
     * 添加用户到设备（包含人脸图片）
     * 如果用户已存在，会先删除再添加，确保数据更新
     */
    public boolean addUserToDeviceWithFace(StudyAttendanceDevice device, SysUser user, String faceImageBase64) {
        String deviceUserNo = String.valueOf(user.getUserId());

        log.info("开始同步用户到设备: 设备[{}], 用户[{}], 用户编号[{}], 是否有人脸图片[{}]",
            device.getDeviceName(), user.getNickName(), deviceUserNo,
            (faceImageBase64 != null && !faceImageBase64.isEmpty()));

        // 先添加用户基本信息
        boolean userAdded = addUserToDevice(device, user, deviceUserNo);
        if (!userAdded) {
            log.error("用户基本信息添加失败: 设备[{}], 用户[{}]", device.getDeviceName(), user.getNickName());
            return false;
        }

        // 如果有人脸图片，则上传人脸图片
        if (faceImageBase64 != null && !faceImageBase64.isEmpty()) {
            boolean faceUploaded = uploadUserFaceImage(device, deviceUserNo, faceImageBase64);
            if (!faceUploaded) {
                log.warn("用户添加成功但人脸图片上传失败: 设备[{}], 用户[{}]", device.getDeviceName(), user.getNickName());
                // 人脸图片上传失败不影响整体结果，用户基本信息已经添加成功
            } else {
                log.info("用户和人脸图片同步成功: 设备[{}], 用户[{}]", device.getDeviceName(), user.getNickName());
            }
        } else {
            log.info("用户同步成功（无人脸图片）: 设备[{}], 用户[{}]", device.getDeviceName(), user.getNickName());
        }

        return true;
    }

    /**
     * 从设备删除用户
     */
    private boolean deleteUserFromDevice(StudyAttendanceDevice device, String deviceUserNo) {
        try {
            String url = buildDeviceUrl(device) + "/ISAPI/AccessControl/UserInfo/Delete?format=json";
            
            // 构建删除请求
            JSONObject deleteRequest = new JSONObject();
            JSONObject userInfoDelCond = new JSONObject();
            
            userInfoDelCond.put("employeeNo", deviceUserNo);
            deleteRequest.put("UserInfoDelCond", userInfoDelCond);
            
            String response = DeviceApiUtil.put(url, deleteRequest.toJSONString(), 
                device.getUsername(), device.getPassword());
            
            if (StringUtils.isNotEmpty(response)) {
                log.debug("用户从设备删除成功: 设备[{}], 设备编号[{}]", 
                    device.getDeviceName(), deviceUserNo);
                return true;
            }
        } catch (Exception e) {
            log.error("用户从设备删除失败: 设备[{}], 设备编号[{}], 错误: {}", 
                device.getDeviceName(), deviceUserNo, e.getMessage());
        }
        return false;
    }
    
    /**
     * 处理考勤事件，转换为系统考勤记录
     * 自动匹配课时，写入考勤表
     */
    private boolean processAttendanceEvent(StudyAttendanceDevice device, JSONObject event) {
        try {
            String employeeNo = event.getString("employeeNoString");
            String eventTime = event.getString("time");
            String eventType = event.getString("name");
            if (StringUtils.isEmpty(employeeNo) || StringUtils.isEmpty(eventTime)) {
                return false;
            }
            // 转换时间格式
            Date attendanceTime = DateUtils.parseDate(eventTime, "yyyy-MM-dd'T'HH:mm:ss'+08:00'");
            // 根据设备用户编号（就是用户ID）查找系统用户
            Long userId;
            try {
                userId = Long.parseLong(employeeNo);
            } catch (NumberFormatException e) {
                log.warn("设备用户编号格式错误: {}", employeeNo);
                return false;
            }
            SysUser user = sysUserMapper.selectUserById(userId);
            if (user == null) {
                log.warn("未找到对应的系统用户: userId={}", userId);
                return false;
            }
            // 匹配用户当天最近的课程（打卡时间在课程开始前30分钟到结束后30分钟内）
            List<StudyRegistration> registrations = registrationMapper.selectStudyRegistrationList(new StudyRegistration() {{ setUserId(userId); }});
            StudyRegistration matchedReg = null;
            Long matchedCourseId = null;
            for (StudyRegistration reg : registrations) {
                StudyClass studyClass = classMapper.selectStudyClassByClassId(reg.getClassId());
                if (studyClass == null) continue;
                // 查询该班级下所有课程
                List<StudyCourse> courseList = courseMapper.selectStudyCourseList(new StudyCourse() {{ setClassId(studyClass.getClassId()); }});
                for (StudyCourse course : courseList) {
                    Date start = new Date(course.getStartTime().getTime() - 30 * 60 * 1000);
                    Date end = new Date(course.getEndTime().getTime() + 30 * 60 * 1000);
                    if (attendanceTime.after(start) && attendanceTime.before(end)) {
                        matchedReg = reg;
                        matchedCourseId = course.getCourseId();
                        break;
                    }
                }
                if (matchedReg != null) break;
            }
            if (matchedReg == null) {
                log.warn("未找到用户当前的报名记录: userId={}, time={}", userId, attendanceTime);
                return false;
            }
            // 创建系统考勤记录
            StudyAttendance attendance = new StudyAttendance();
            attendance.setUserId(userId);
            attendance.setRegistrationId(matchedReg.getRegistrationId());
            attendance.setClassId(matchedReg.getClassId());
            attendance.setCourseId(matchedCourseId);
            attendance.setCheckInTime(attendanceTime);
            attendance.setCheckInSource("0"); // 考勤机
            attendance.setAttendanceStatus("正常");
            attendance.setCreateTime(new Date());
            attendanceMapper.insertStudyAttendance(attendance);
            log.info("考勤记录同步成功: 设备[{}], 用户[{}], 时间[{}]", device.getDeviceName(), user.getNickName(), attendanceTime);
            return true;
        } catch (Exception e) {
            log.error("处理考勤事件失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 同步所有用户到指定设备
     * 用于手动同步或异常恢复
     */
    public int syncAllUsersToDevice(Long deviceId) {
        try {
            StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                log.warn("设备不存在: deviceId={}", deviceId);
                return 0;
            }
            
            // 获取所有启用的用户
            SysUser userQuery = new SysUser();
            userQuery.setStatus("0"); // 启用状态
            List<SysUser> allUsers = sysUserMapper.selectUserList(userQuery);
            if (allUsers.isEmpty()) {
                log.warn("没有启用的用户可同步");
                return 0;
            }
            
            int syncCount = syncStudentsToDevice(device, allUsers);
            log.info("手动同步所有用户到设备[{}]完成: 成功{}人", device.getDeviceName(), syncCount);
            return syncCount;
            
        } catch (Exception e) {
            log.error("同步所有用户到设备失败: deviceId={}, 错误: {}", deviceId, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 清除设备所有用户
     * 用于设备重置或测试
     */
    public int clearAllUsersFromDevice(Long deviceId) {
        try {
            StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                log.warn("设备不存在: deviceId={}", deviceId);
                return 0;
            }
            
            // 获取设备上的所有用户（这里简化为获取所有系统用户）
            SysUser userQuery = new SysUser();
            userQuery.setStatus("0"); // 启用状态
            List<SysUser> allUsers = sysUserMapper.selectUserList(userQuery);
            if (allUsers.isEmpty()) {
                log.warn("没有用户需要清除");
                return 0;
            }
            
            int removeCount = removeStudentsFromDevice(device, allUsers);
            log.info("清除设备[{}]所有用户完成: 清除{}人", device.getDeviceName(), removeCount);
            return removeCount;
            
        } catch (Exception e) {
            log.error("清除设备所有用户失败: deviceId={}, 错误: {}", deviceId, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 同步单个用户到指定设备
     */
    public boolean syncSingleUserToDevice(Long deviceId, Long userId) {
        try {
            StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                log.warn("设备不存在: deviceId={}", deviceId);
                return false;
            }
            
            SysUser user = sysUserMapper.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }
            
            // 使用用户ID作为设备用户编号
            String deviceUserNo = String.valueOf(userId);
            boolean result = addUserToDevice(device, user, deviceUserNo);
            
            if (result) {
                log.info("同步用户[{}]到设备[{}]成功", user.getUserName(), device.getDeviceName());
            } else {
                log.warn("同步用户[{}]到设备[{}]失败", user.getUserName(), device.getDeviceName());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("同步单个用户到设备失败: deviceId={}, userId={}, 错误: {}", deviceId, userId, e.getMessage());
            return false;
        }
    }
    
    /**
     * 从指定设备删除单个用户
     */
    public boolean removeSingleUserFromDevice(Long deviceId, Long userId) {
        try {
            StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                log.warn("设备不存在: deviceId={}", deviceId);
                return false;
            }
            
            SysUser user = sysUserMapper.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }
            
            // 使用用户ID作为设备用户编号
            String deviceUserNo = String.valueOf(userId);
            boolean result = deleteUserFromDevice(device, deviceUserNo);
            
            if (result) {
                log.info("从设备[{}]删除用户[{}]成功", device.getDeviceName(), user.getUserName());
            } else {
                log.warn("从设备[{}]删除用户[{}]失败", device.getDeviceName(), user.getUserName());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("从设备删除单个用户失败: deviceId={}, userId={}, 错误: {}", deviceId, userId, e.getMessage());
            return false;
        }
    }

    /**
     * 构建设备URL
     */
    private String buildDeviceUrl(StudyAttendanceDevice device) {
        String protocol = "http"; // 默认使用HTTP
        String port = StringUtils.isNotEmpty(device.getDevicePort()) ? ":" + device.getDevicePort() : ":80";
        return protocol + "://" + device.getDeviceIp() + port;
    }

    /**
     * 同步单个用户到所有可用考勤机（含人脸图片）
     */
    public void syncUserToAllDevices(SysUser user) {
        // 查询所有可用设备（status=0, delFlag=0）
        StudyAttendanceDevice query = new StudyAttendanceDevice();
        query.setStatus("0");
        query.setDelFlag("0");
        List<StudyAttendanceDevice> deviceList = deviceMapper.selectStudyAttendanceDeviceList(query);
        if (deviceList == null || deviceList.isEmpty()) return;
        // 获取头像base64
        String faceBase64 = null;
        if (user.getAvatar() != null && !user.getAvatar().isEmpty()) {
            try {
                byte[] imgBytes = com.base.common.utils.file.ImageUtils.getImage(user.getAvatar());
                if (imgBytes != null) {
                    faceBase64 = java.util.Base64.getEncoder().encodeToString(imgBytes);
                    log.info("用户[{}]头像已转为base64，准备同步到考勤机", user.getUserName());
                } else {
                    log.warn("用户[{}]头像图片读取失败，路径: {}", user.getUserName(), user.getAvatar());
                }
            } catch (Exception e) {
                log.warn("用户[{}]头像转base64失败: {}", user.getUserName(), e.getMessage());
            }
        } else {
            log.info("用户[{}]无头像，不同步人脸图片", user.getUserName());
        }
        for (StudyAttendanceDevice device : deviceList) {
            addUserToDeviceWithFace(device, user, faceBase64);
        }
    }

    /**
     * 从所有可用考勤机删除用户
     */
    public void removeUserFromAllDevices(Long userId) {
        StudyAttendanceDevice query = new StudyAttendanceDevice();
        query.setStatus("0");
        query.setDelFlag("0");
        List<StudyAttendanceDevice> deviceList = deviceMapper.selectStudyAttendanceDeviceList(query);
        if (deviceList == null || deviceList.isEmpty()) return;
        for (StudyAttendanceDevice device : deviceList) {
            deleteUserFromDevice(device, String.valueOf(userId));
        }
    }

    /**
     * 测试人脸图片上传功能
     * 用于调试和验证API调用是否正确
     */
    public boolean testFaceUpload(Long deviceId, String testUserNo, String testBase64) {
        try {
            StudyAttendanceDevice device = deviceMapper.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                log.warn("测试设备不存在: deviceId={}", deviceId);
                return false;
            }

            // 使用简单的测试base64数据（如果没有提供）
            String faceBase64 = testBase64;
            if (StringUtils.isEmpty(faceBase64)) {
                // 使用一个简单的1x1像素的透明PNG图片的base64
                faceBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
            }

            log.info("开始测试人脸图片上传: 设备[{}], 测试用户编号[{}]", device.getDeviceName(), testUserNo);
            boolean result = uploadUserFaceImage(device, testUserNo, faceBase64);

            if (result) {
                log.info("人脸图片上传测试成功: 设备[{}], 测试用户编号[{}]", device.getDeviceName(), testUserNo);
            } else {
                log.warn("人脸图片上传测试失败: 设备[{}], 测试用户编号[{}]", device.getDeviceName(), testUserNo);
            }

            return result;

        } catch (Exception e) {
            log.error("人脸图片上传测试异常: deviceId={}, testUserNo={}, 错误: {}", deviceId, testUserNo, e.getMessage());
            return false;
        }
    }
}